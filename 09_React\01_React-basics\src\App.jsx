import { useState } from 'react'
import Header from './components/Header'
import Card from './components/Card.jsx'
import Footer from './components/Footer.jsx'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import './App.css'

function App() {
  const [count, setCount] = useState(0)

  return (
    <>
      <Header />
      <div>
        <Card title="Dobrodošli!">
          <p>Ovo je naša prva stranica složena od komponenti</p>
        </Card>
        <Card title="ponovna upotreba">
          <ul>
            <li> List item 1</li>
            <li> List item 2</li>
            <li> List item 3</li>
          </ul>
        </Card>
        <Card title="Treća kartica">
          <p>Možemo staviti bilo što unutar komponente</p>
        </Card>
      </div>
      <div>
        <a href="https://vite.dev" target="_blank">
          <img src={viteLogo} className="logo" alt="Vite logo" />
        </a>
        <a href="https://react.dev" target="_blank">
          <img src={reactLogo} className="logo react" alt="React logo" />
        </a>
      </div>
      <h1>Vite + React</h1>
      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>
          count is {count}
        </button>
        <p>
          Edit <code>src/App.jsx</code> and save to test HMR
        </p>
      </div>
      <p className="read-the-docs">
        Click on the Vite and React logos to learn more
      </p>
    </>
  )
}

export default App
